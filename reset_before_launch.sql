-- RESET SCRIPT FOR LAUNCH
-- Run this in Supabase SQL Editor to clean up test data before sending creator emails
-- This preserves <PERSON>'s account as #1 with his historical first audio post

-- 1. First, let's see what accounts exist before reset
SELECT
    name,
    email,
    created_at,
    has_day1_badge,
    signup_number
FROM users
ORDER BY created_at ASC;

-- 2. Reset the Day 1 badge system (preserves <PERSON> as #1)
SELECT reset_for_launch();

-- 3. Check the results after reset
SELECT
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE has_day1_badge = TRUE) as day1_badge_holders,
    MIN(signup_number) as first_signup_number,
    MAX(signup_number) as last_signup_number
FROM users;

-- 4. Verify <PERSON> is #1 with Day 1 badge
SELECT
    name,
    email,
    signup_number,
    has_day1_badge,
    created_at
FROM users
WHERE name = '<PERSON>' OR email LIKE '%dweave%' OR email LIKE '%david%';

-- 5. Show all remaining users after cleanup
SELECT
    name,
    email,
    signup_number,
    has_day1_badge,
    created_at
FROM users
ORDER BY signup_number ASC;
