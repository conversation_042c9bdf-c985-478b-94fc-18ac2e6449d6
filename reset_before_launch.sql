-- RESET SCRIPT FOR LAUNCH
-- Run this in Supabase SQL Editor to clean up test data before sending creator emails

-- 1. Reset the Day 1 badge system
SELECT reset_for_launch();

-- 2. Check the results
SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE has_day1_badge = TRUE) as day1_badge_holders,
    MIN(signup_number) as first_signup_number,
    MAX(signup_number) as last_signup_number
FROM users;

-- 3. Show first 10 Day 1 badge holders (if any)
SELECT 
    name,
    email,
    signup_number,
    created_at
FROM users 
WHERE has_day1_badge = TRUE 
ORDER BY signup_number 
LIMIT 10;
