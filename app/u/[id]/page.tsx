import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import { UnifiedProfileClient } from "@/components/UnifiedProfileClient"

interface UnifiedProfilePageProps {
  params: Promise<{
    id: string
  }>
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}


export default async function UnifiedProfilePage({ params }: UnifiedProfilePageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()
  
  // Check if current user is authenticated and get subscription status
  const { data: { user } } = await supabase.auth.getUser()
  
  // Check if user has active subscription to this writer
  let hasActiveSubscription = false
  let isFollowing = false
  if (user) {
    const { data: subscription } = await supabase
      .from("subscriptions")
      .select("id, current_period_end")
      .or(`subscriber_id.eq.${user.id},reader_id.eq.${user.id}`)
      .eq("writer_id", id)
      .eq("status", "active")
      .gte("current_period_end", new Date().toISOString())
      .single()
    
    hasActiveSubscription = !!subscription

    // Check if user is following this writer
    const { data: follow } = await supabase
      .from("follows")
      .select("id")
      .eq("follower_id", user.id)
      .eq("writer_id", id)
      .single()
    
    isFollowing = !!follow
  }
  
  // Get writer data directly from users table first
  const { data: writerData, error: writerError } = await supabase
    .from('users')
    .select('id, name, avatar, profile_picture_url, bio, price_monthly, role, custom_url, subscriber_count, follower_count, stripe_account_id, has_day1_badge, signup_number')
    .eq('id', id)
    .single()

  console.log('Profile query result - writerData:', writerData)
  console.log('Profile query result - badge fields:', {
    has_day1_badge: writerData?.has_day1_badge,
    signup_number: writerData?.signup_number,
    name: writerData?.name
  })

  if (writerError || !writerData) {
    console.error('Writer lookup error:', writerError)
    notFound()
  }

  // All authenticated users can have profiles in the unified system
  if (writerData.role !== 'user' && writerData.role !== 'admin') {
    notFound()
  }
  
  // Get free entry for this writer
  const { data: freeEntry } = await supabase
    .from('diary_entries')
    .select('id, title, body_md, created_at')
    .eq('user_id', id)
    .eq('is_free', true)
    .eq('is_hidden', false)
    .single()
  
  // Use the raw user data for UnifiedProfileClient
  const writer = {
    id: writerData.id,
    name: writerData.name,
    avatar: writerData.avatar,
    profile_picture_url: writerData.profile_picture_url,
    bio: writerData.bio,
    price_monthly: writerData.price_monthly,
    stripe_account_id: writerData.stripe_account_id,
    subscriber_count: writerData.subscriber_count,
    follower_count: writerData.follower_count || 0,
    custom_url: writerData.custom_url,
    free_entry_id: freeEntry?.id || null,
    free_entry_title: freeEntry?.title || null,
    free_entry_body_md: freeEntry?.body_md || null,
    free_entry_created_at: freeEntry?.created_at || null
  }
  
  // Get all diary entries (free and locked) for this writer
  const { data: allEntries, error: entriesError } = await supabase
    .from("diary_entries")
    .select("id, title, body_md, is_free, view_count, created_at")
    .eq("user_id", id)
    .eq("is_hidden", false)
    .order("created_at", { ascending: false })

  if (entriesError) {
    console.error('Error fetching entries:', entriesError)
  }

  // Get all book projects for this writer
  const { data: projects, error: projectsError } = await supabase
    .from("projects")
    .select(`
      id,
      title,
      description,
      cover_image_url,
      genre,
      is_private,
      is_complete,
      price_type,
      price_amount,
      total_chapters,
      total_words,
      created_at,
      updated_at
    `)
    .eq("user_id", id)
    .eq("is_private", false)
    .order("updated_at", { ascending: false })

  if (projectsError) {
    console.error('Error fetching projects:', projectsError)
  }

  // Get all audio posts for this writer
  const { data: audioPosts, error: audioError } = await supabase
    .from("audio_posts")
    .select(`
      id,
      audio_url,
      description,
      duration_seconds,
      love_count,
      reply_count,
      created_at,
      user:users!user_id (
        id,
        name,
        avatar,
        profile_picture_url,
        has_day1_badge,
        signup_number
      )
    `)
    .eq("user_id", id)
    .order("created_at", { ascending: false })

  if (audioError) {
    console.error('Error fetching audio posts:', audioError)
  }
  
  return (
    <UnifiedProfileClient
      user={writer}
      diaryEntries={allEntries || []}
      projects={projects || []}
      audioPosts={audioPosts || []}
      hasActiveSubscription={hasActiveSubscription}
      isFollowing={isFollowing}
      isOwnProfile={user?.id === id}
    />
  )
}