-- Day 1 Badge System for First 500 Signups
-- Run this in Supabase SQL Editor BEFORE sending creator emails

-- 1. Add Day 1 badge fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS has_day1_badge BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS signup_number INTEGER;

-- 2. <PERSON><PERSON> function to automatically assign Day 1 badges to first 500 signups
CREATE OR REPLACE FUNCTION assign_day1_badge()
RETURNS TRIGGER AS $$
DECLARE
    current_signup_count INTEGER;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM users WHERE id != NEW.id;
    
    -- Assign signup number (1-based)
    NEW.signup_number := current_signup_count + 1;
    
    -- Assign Day 1 badge if within first 500 signups
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
    ELSE
        NEW.has_day1_badge := FALSE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. <PERSON><PERSON> trigger to run the function on user signup
DROP TRIGGER IF EXISTS assign_day1_badge_trigger ON users;
CREATE TRIGGER assign_day1_badge_trigger
    BEFORE INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION assign_day1_badge();

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_signup_number ON users(signup_number);
CREATE INDEX IF NOT EXISTS idx_users_day1_badge ON users(has_day1_badge) WHERE has_day1_badge = TRUE;

-- 5. Create function to get Day 1 badge holders (for admin/analytics)
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.created_at
    FROM users u
    WHERE u.has_day1_badge = TRUE
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to get current Day 1 badge count
CREATE OR REPLACE FUNCTION get_day1_badge_count()
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM users WHERE has_day1_badge = TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Grant permissions
GRANT EXECUTE ON FUNCTION get_day1_badge_holders() TO authenticated;
GRANT EXECUTE ON FUNCTION get_day1_badge_count() TO authenticated;

-- 8. Create function to reset everything for launch (USE CAREFULLY)
-- This preserves David Weaver's account as #1 and cleans up test accounts
CREATE OR REPLACE FUNCTION reset_for_launch()
RETURNS void AS $$
BEGIN
    -- First, delete test accounts (but preserve David Weaver and any real creators)
    -- Adjust the WHERE clause based on what test accounts you want to remove
    DELETE FROM users WHERE
        email LIKE '%test%'
        OR email LIKE '%example%'
        OR email LIKE '%temp%'
        OR name LIKE '%test%'
        -- Add other patterns for test accounts you want to remove
        -- BUT make sure David Weaver's email/name doesn't match these patterns!
    ;

    -- Reset all remaining signup numbers and Day 1 badges
    UPDATE users SET
        signup_number = NULL,
        has_day1_badge = FALSE;

    -- Reassign based on created_at order (David Weaver should be oldest = #1)
    WITH numbered_users AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at ASC) as new_signup_number
        FROM users
    )
    UPDATE users
    SET
        signup_number = numbered_users.new_signup_number,
        has_day1_badge = CASE WHEN numbered_users.new_signup_number <= 500 THEN TRUE ELSE FALSE END
    FROM numbered_users
    WHERE users.id = numbered_users.id;

    -- Ensure David Weaver gets signup #1 and Day 1 badge (safety check)
    UPDATE users SET
        signup_number = 1,
        has_day1_badge = TRUE
    WHERE name = 'David Weaver' OR email LIKE '%dweave%' OR email LIKE '%david%';

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Grant permission for reset function
GRANT EXECUTE ON FUNCTION reset_for_launch() TO authenticated;

-- 10. Add helpful comments
COMMENT ON COLUMN users.has_day1_badge IS 'Permanent badge for first 500 signups - bragging rights forever';
COMMENT ON COLUMN users.signup_number IS 'Sequential signup number starting from 1';

-- 11. Test the system (optional - run to verify it works)
-- SELECT get_day1_badge_count() as current_day1_badges;
-- SELECT * FROM get_day1_badge_holders() LIMIT 10;
