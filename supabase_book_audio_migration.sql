-- Book Audio Discussions Migration
-- Run this in Supabase SQL Editor

-- 1. Create book_audio_posts table
CREATE TABLE book_audio_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL,
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL DEFAULT 0,
    waveform_data JSONB,
    
    -- Position tracking within chapter
    chapter_position INTEGER DEFAULT 0,
    page_context TEXT,
    
    -- Content
    description TEXT CHECK (LENGTH(description) <= 50),
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create book_audio_replies table
CREATE TABLE book_audio_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL,
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL DEFAULT 0,
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create book_audio_loves table
CREATE TABLE book_audio_loves (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE,
    book_audio_reply_id UUID REFERENCES book_audio_replies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure user can only love a post OR reply, not both
    CHECK (
        (book_audio_post_id IS NOT NULL AND book_audio_reply_id IS NULL) OR
        (book_audio_post_id IS NULL AND book_audio_reply_id IS NOT NULL)
    ),
    -- Ensure unique love per user per post/reply
    UNIQUE(user_id, book_audio_post_id),
    UNIQUE(user_id, book_audio_reply_id)
);

-- 4. Create indexes for performance
CREATE INDEX idx_book_audio_posts_project_chapter ON book_audio_posts(project_id, chapter_id);
CREATE INDEX idx_book_audio_posts_user ON book_audio_posts(user_id);
CREATE INDEX idx_book_audio_posts_created_at ON book_audio_posts(created_at DESC);
CREATE INDEX idx_book_audio_replies_post ON book_audio_replies(book_audio_post_id);
CREATE INDEX idx_book_audio_replies_user ON book_audio_replies(user_id);

-- 5. Enable RLS
ALTER TABLE book_audio_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_audio_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_audio_loves ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies
-- Users can view audio posts for books they have access to
CREATE POLICY "Users can view book audio posts they have access to" ON book_audio_posts
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p 
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

-- Users can create audio posts for books they have access to
CREATE POLICY "Users can create book audio posts for accessible books" ON book_audio_posts
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        project_id IN (
            SELECT p.id FROM projects p 
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

-- Users can update their own posts
CREATE POLICY "Users can update their own book audio posts" ON book_audio_posts
    FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own posts
CREATE POLICY "Users can delete their own book audio posts" ON book_audio_posts
    FOR DELETE USING (user_id = auth.uid());

-- Similar policies for replies
CREATE POLICY "Users can view book audio replies for accessible books" ON book_audio_replies
    FOR SELECT USING (
        book_audio_post_id IN (
            SELECT bap.id FROM book_audio_posts bap
            JOIN projects p ON p.id = bap.project_id
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

CREATE POLICY "Users can create book audio replies for accessible books" ON book_audio_replies
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        book_audio_post_id IN (
            SELECT bap.id FROM book_audio_posts bap
            JOIN projects p ON p.id = bap.project_id
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

CREATE POLICY "Users can update their own book audio replies" ON book_audio_replies
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own book audio replies" ON book_audio_replies
    FOR DELETE USING (user_id = auth.uid());

-- Love policies
CREATE POLICY "Users can view book audio loves" ON book_audio_loves
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own book audio loves" ON book_audio_loves
    FOR ALL USING (user_id = auth.uid());

-- 7. Create triggers to update reply counts
CREATE OR REPLACE FUNCTION update_book_audio_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE book_audio_posts
        SET reply_count = reply_count + 1
        WHERE id = NEW.book_audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE book_audio_posts
        SET reply_count = reply_count - 1
        WHERE id = OLD.book_audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_reply_count_trigger
    AFTER INSERT OR DELETE ON book_audio_replies
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_reply_count();

-- 8. Create triggers to update love counts
CREATE OR REPLACE FUNCTION update_book_audio_love_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_post_id;
        ELSIF NEW.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_post_id;
        ELSIF OLD.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_love_count_trigger
    AFTER INSERT OR DELETE ON book_audio_loves
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_love_count();
