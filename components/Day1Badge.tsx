"use client"

import { <PERSON>, Star, Zap } from "lucide-react"

interface Day1BadgeProps {
  signupNumber?: number
  className?: string
  size?: "sm" | "md" | "lg"
  showTooltip?: boolean
}

export function Day1Badge({
  signupNumber,
  className = "",
  size = "md",
  showTooltip = true
}: Day1BadgeProps) {
  const sizeClasses = {
    sm: "text-xs px-1.5 py-0.5",
    md: "text-sm px-2 py-1",
    lg: "text-base px-3 py-1.5"
  }

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16
  }

  return (
    <div className="relative inline-block">
      <div
        className={`
          bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold
          border-0 shadow-lg hover:shadow-xl transition-all duration-200
          flex items-center gap-1 rounded-full
          ${sizeClasses[size]}
          ${className}
        `}
        title={showTooltip ? `Day 1 Member #${signupNumber} - One of the first 500 OnlyDiary creators` : undefined}
      >
        <Crown size={iconSizes[size]} className="text-yellow-300" />
        <span>Day 1</span>
        {signupNumber && size !== "sm" && (
          <span className="text-yellow-300 font-bold">#{signupNumber}</span>
        )}
      </div>

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-sm opacity-30 -z-10" />
    </div>
  )
}

// Alternative badge designs
export function Day1BadgeMinimal({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-purple-100 text-purple-800 border border-purple-300
        font-medium text-xs rounded-full px-2 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Member #${signupNumber}`}
    >
      <Star size={12} />
      Day 1
    </div>
  )
}

export function Day1BadgeGold({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900
        font-bold text-sm border-0 shadow-md rounded-full px-3 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Founder #${signupNumber} - One of the first 500`}
    >
      <Zap size={14} className="text-yellow-800" />
      Founder
      {signupNumber && <span>#{signupNumber}</span>}
    </div>
  )
}

// Hook to check if user has Day 1 badge
export function useDay1Badge(user: any) {
  return {
    hasDay1Badge: user?.has_day1_badge || false,
    signupNumber: user?.signup_number || null,
    isEarlyAdopter: user?.signup_number && user.signup_number <= 500
  }
}
