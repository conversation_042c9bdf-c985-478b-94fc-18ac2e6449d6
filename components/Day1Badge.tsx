"use client"

import { <PERSON>, <PERSON>, Zap } from "lucide-react"

interface Day1BadgeProps {
  signupNumber?: number
  badgeTier?: string
  className?: string
  size?: "sm" | "md" | "lg"
  showTooltip?: boolean
}

interface BadgeConfig {
  name: string
  gradient: string
  icon: any
  iconColor: string
  range: string
  description: string
}

export function Day1Badge({
  signupNumber,
  badgeTier = "day1",
  className = "",
  size = "md",
  showTooltip = true
}: Day1BadgeProps) {
  const sizeClasses = {
    sm: "text-xs px-1.5 py-0.5",
    md: "text-sm px-2 py-1",
    lg: "text-base px-3 py-1.5"
  }

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16
  }

  // Badge configurations for each tier
  const getBadgeConfig = (tier: string): BadgeConfig => {
    const configs: Record<string, BadgeConfig> = {
      day1: {
        name: "Day 1",
        gradient: "from-purple-600 to-pink-600",
        icon: Crown,
        iconColor: "text-yellow-300",
        range: "1-500",
        description: "One of the first 500 OnlyDiary creators"
      },
      pioneer: {
        name: "Pioneer",
        gradient: "from-yellow-500 to-orange-500",
        icon: Star,
        iconColor: "text-white",
        range: "501-1,000",
        description: "Pioneer member of OnlyDiary"
      },
      founder: {
        name: "Founder",
        gradient: "from-gray-400 to-gray-600",
        icon: Zap,
        iconColor: "text-blue-200",
        range: "1,001-2,500",
        description: "Founding member of OnlyDiary"
      },
      early_adopter: {
        name: "Early Adopter",
        gradient: "from-amber-600 to-amber-800",
        icon: Star,
        iconColor: "text-amber-200",
        range: "2,501-5,000",
        description: "Early adopter of OnlyDiary"
      },
      charter_member: {
        name: "Charter",
        gradient: "from-blue-500 to-blue-700",
        icon: Crown,
        iconColor: "text-blue-200",
        range: "5,001-10,000",
        description: "Charter member of OnlyDiary"
      }
    }
    return configs[tier] || configs.day1
  }

  const config = getBadgeConfig(badgeTier)
  const IconComponent = config.icon

  return (
    <div className="relative inline-block">
      <div
        className={`
          bg-gradient-to-r ${config.gradient} text-white font-semibold
          border-0 shadow-lg hover:shadow-xl transition-all duration-200
          flex items-center gap-1 rounded-full
          ${sizeClasses[size]}
          ${className}
        `}
        title={showTooltip ? `${config.name} Member #${signupNumber} - ${config.description}` : undefined}
      >
        <IconComponent size={iconSizes[size]} className={config.iconColor} />
        <span>{config.name}</span>
        {signupNumber && size !== "sm" && (
          <span className={`${config.iconColor} font-bold`}>#{signupNumber}</span>
        )}
      </div>

      {/* Subtle glow effect */}
      <div className={`absolute inset-0 bg-gradient-to-r ${config.gradient} rounded-full blur-sm opacity-30 -z-10`} />
    </div>
  )
}

// Alternative badge designs
export function Day1BadgeMinimal({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-purple-100 text-purple-800 border border-purple-300
        font-medium text-xs rounded-full px-2 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Member #${signupNumber}`}
    >
      <Star size={12} />
      Day 1
    </div>
  )
}

export function Day1BadgeGold({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900
        font-bold text-sm border-0 shadow-md rounded-full px-3 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Founder #${signupNumber} - One of the first 500`}
    >
      <Zap size={14} className="text-yellow-800" />
      Founder
      {signupNumber && <span>#{signupNumber}</span>}
    </div>
  )
}

// Hook to check if user has Day 1 badge
export function useDay1Badge(user: any) {
  return {
    hasDay1Badge: user?.has_day1_badge || false,
    signupNumber: user?.signup_number || null,
    isEarlyAdopter: user?.signup_number && user.signup_number <= 500
  }
}
