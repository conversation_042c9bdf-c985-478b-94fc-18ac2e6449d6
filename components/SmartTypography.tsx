'use client'

import { useEffect, useRef } from 'react'

interface SmartTypographyProps {
  content: string
  className?: string
  isPreview?: boolean
  zenMode?: boolean
  isDesktop?: boolean
  enableAdvancedFeatures?: boolean
}

export function SmartTypography({
  content,
  className = '',
  isPreview = false,
  zenMode = false,
  isDesktop = false,
  enableAdvancedFeatures = true
}: SmartTypographyProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // Smart formatting algorithm
  const formatContent = (text: string): string => {
    if (!text) return ''

    // Clean the content first - remove invisible characters, cursor markers, and artifacts
    let cleanedText = text
      // Remove zero-width characters and other invisible Unicode characters
      .replace(/[\u200B-\u200D\uFEFF]/g, '')
      // Remove any cursor markers or block elements that might be artifacts
      .replace(/^[\s\u00A0]*[|\u2502\u2503\u2588\u258C\u2590]*[\s\u00A0]*/, '')
      // Remove leading/trailing whitespace and normalize line breaks
      .replace(/^\s+|\s+$/g, '')
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove any HTML-like artifacts that might be cursor-related
      .replace(/^<[^>]*>/, '')
      // Remove any leading punctuation that might be cursor artifacts
      .replace(/^[^\w\s"'"'"]*/, '')

    // Split into paragraphs, preserving intentional breaks
    let paragraphs = cleanedText.split(/\n\s*\n/)
    
    // Clean up each paragraph
    paragraphs = paragraphs.map(para => {
      // Remove excessive whitespace but preserve single line breaks
      let cleanPara = para.trim()
        // Remove any remaining cursor artifacts at paragraph level
        .replace(/^[\s\u00A0]*[|\u2502\u2503\u2588\u258C\u2590]+[\s\u00A0]*/, '')
        // Remove zero-width spaces and other invisible characters
        .replace(/[\u200B-\u200D\uFEFF]/g, '')
        // Convert line breaks to HTML
        .replace(/\n/g, '<br>')

      return cleanPara
    }).filter(para => para.length > 0)

    // Apply smart spacing based on content analysis
    const formattedParagraphs = paragraphs.map((para, index) => {
      const isShort = para.length < 100
      const isQuote = para.startsWith('"') || para.includes('"')
      const isDialogue = para.includes('"') && para.includes('"')
      const isListItem = para.match(/^[-•*]\s/) || para.match(/^\d+\.\s/)
      
      let spacing = 'mb-4' // Default spacing
      
      // Adjust spacing based on content type
      if (isShort && !isQuote) {
        spacing = 'mb-3' // Tighter for short paragraphs
      } else if (isQuote || isDialogue) {
        spacing = 'mb-5' // More space around quotes
      } else if (isListItem) {
        spacing = 'mb-2' // Tighter for list items
      } else if (para.length > 500) {
        spacing = 'mb-6' // More space after long paragraphs
      }

      // Add special styling for different content types
      let extraClasses = ''
      if (isQuote) {
        extraClasses = 'italic pl-4 border-l-2 border-gray-300'
      } else if (isDialogue) {
        extraClasses = 'text-gray-800'
      }

      return `<p class="${spacing} ${extraClasses} leading-relaxed">${para}</p>`
    })

    return formattedParagraphs.join('')
  }

  // Advanced typography optimization
  useEffect(() => {
    if (!containerRef.current || !enableAdvancedFeatures) return

    const container = containerRef.current
    const paragraphs = container.querySelectorAll('p')

    paragraphs.forEach((p, index) => {
      // Prevent orphans and widows
      const words = p.textContent?.split(' ') || []
      if (words.length > 1) {
        const lastTwoWords = words.slice(-2).join('&nbsp;')
        const restOfText = words.slice(0, -2).join(' ')
        if (restOfText) {
          p.innerHTML = p.innerHTML.replace(
            words.slice(-2).join(' '),
            lastTwoWords
          )
        }
      }

      // Desktop-specific enhancements
      if (isDesktop && window.innerWidth >= 1024) {
        // Enhanced typography for large screens
        p.style.fontSize = zenMode ? '1.25rem' : '1.125rem'
        p.style.lineHeight = zenMode ? '2' : '1.8'
        p.style.marginBottom = zenMode ? '2.5rem' : '1.5rem'

        // Advanced text features
        p.style.textRendering = 'optimizeLegibility'
        p.style.fontFeatureSettings = '"kern" 1, "liga" 1, "calt" 1'
        p.style.fontVariantLigatures = 'common-ligatures'

        // Subtle text shadow for depth
        if (zenMode) {
          p.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.1)'
        }
      } else if (zenMode) {
        // Zen mode for smaller screens
        p.style.lineHeight = '1.8'
        p.style.marginBottom = '2rem'
      } else {
        // Standard mobile/tablet
        p.style.lineHeight = '1.7'
        p.style.marginBottom = '1rem'
      }

      // Mobile optimization
      if (window.innerWidth < 768) {
        p.style.fontSize = '16px' // Prevent zoom on iOS
        p.style.lineHeight = '1.6'
      }

      // Desktop reading enhancements
      if (isDesktop && enableAdvancedFeatures) {
        // Hover effects for better reading focus
        p.addEventListener('mouseenter', () => {
          if (!zenMode) {
            p.style.backgroundColor = 'rgba(59, 130, 246, 0.05)'
            p.style.borderRadius = '4px'
            p.style.padding = '0.5rem'
            p.style.margin = '0 -0.5rem 1.5rem -0.5rem'
            p.style.transition = 'all 0.2s ease'
          }
        })

        p.addEventListener('mouseleave', () => {
          if (!zenMode) {
            p.style.backgroundColor = 'transparent'
            p.style.padding = '0'
            p.style.margin = '0 0 1.5rem 0'
          }
        })
      }
    })
  }, [content, zenMode, isDesktop, enableAdvancedFeatures])

  const formattedContent = formatContent(content)

  return (
    <div
      ref={containerRef}
      className={`prose prose-gray max-w-none ${className} ${
        zenMode 
          ? 'prose-lg prose-invert text-white/90' 
          : 'prose-base text-gray-900'
      }`}
      style={{
        // Optimal reading width
        maxWidth: zenMode ? '65ch' : '70ch',
        // Perfect line height for readability
        lineHeight: zenMode ? '1.8' : '1.7',
        // Smooth text rendering
        textRendering: 'optimizeLegibility',
        fontFeatureSettings: '"kern" 1, "liga" 1',
        // Hyphenation for better text flow
        hyphens: 'auto',
        // Prevent text selection issues
        WebkitFontSmoothing: 'antialiased',
        MozOsxFontSmoothing: 'grayscale',
      }}
      dangerouslySetInnerHTML={{ __html: formattedContent }}
    />
  )
}

// Real-time formatting for the editor
export function useSmartFormatting(content: string, onChange: (formatted: string) => void) {
  useEffect(() => {
    const formatTimer = setTimeout(() => {
      // Auto-format while typing (subtle improvements)
      let formatted = content
      
      // Fix common spacing issues
      formatted = formatted
        .replace(/\n{3,}/g, '\n\n') // Max 2 line breaks
        .replace(/\s+$/gm, '') // Remove trailing spaces
        .replace(/^\s+/gm, '') // Remove leading spaces (except intentional indents)
      
      // Only update if there are actual changes
      if (formatted !== content) {
        onChange(formatted)
      }
    }, 1000) // Debounce for 1 second

    return () => clearTimeout(formatTimer)
  }, [content, onChange])
}

// Typography metrics for analytics
export function getTypographyMetrics(content: string) {
  const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
  const words = content.split(/\s+/).filter(w => w.length > 0)
  const sentences = content.split(/[.!?]+/).filter(s => s.trim())
  
  const avgWordsPerParagraph = paragraphs.length > 0 ? words.length / paragraphs.length : 0
  const avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0
  const readingTime = Math.ceil(words.length / 200) // 200 WPM average
  
  return {
    paragraphs: paragraphs.length,
    words: words.length,
    sentences: sentences.length,
    avgWordsPerParagraph: Math.round(avgWordsPerParagraph),
    avgWordsPerSentence: Math.round(avgWordsPerSentence),
    readingTime,
    readabilityScore: calculateReadabilityScore(avgWordsPerSentence, content)
  }
}

function calculateReadabilityScore(avgWordsPerSentence: number, content: string): string {
  // Simple readability assessment
  const complexWords = content.split(/\s+/).filter(word => word.length > 6).length
  const totalWords = content.split(/\s+/).length
  const complexWordRatio = totalWords > 0 ? complexWords / totalWords : 0
  
  if (avgWordsPerSentence < 15 && complexWordRatio < 0.15) return 'Very Easy'
  if (avgWordsPerSentence < 20 && complexWordRatio < 0.20) return 'Easy'
  if (avgWordsPerSentence < 25 && complexWordRatio < 0.25) return 'Moderate'
  if (avgWordsPerSentence < 30 && complexWordRatio < 0.30) return 'Difficult'
  return 'Very Difficult'
}
